"""End-to-end orchestrator for the multi-tenant email-to-OneDrive pipeline.

Usage (interactive):
    python main.py [--once]

If --once is supplied the script processes unread e-mails once and exits.
Otherwise it runs forever with a sleep between cycles (default 5 min).
"""
from __future__ import annotations

import argparse
import time
from datetime import datetime
from collections import defaultdict
from typing import List, Tuple, Dict, Any

from core.tenant_loader import list_tenants
from core.mail_reader import read_mail, read_mail_multi_mailbox, get_tenant_mail_headers
from core.unified_file_analyzer import analyze_file_bytes
from core.mailbox_manager import MailboxConfigManager
from core.tracking.config import setup_tracking_for_tenant

CYCLE_SECONDS = 60*60  # Waiting 1 hour between cycles seconds


def group_attachments_by_email_and_type(attachments: List[Tuple], tenant_config: Dict[str, Any]) -> Dict[str, Dict[str, List[Tuple]]]:
    """
    Group attachments by email (sender + received time) and then by document type.

    Args:
        attachments: List of attachment tuples from mail reader
        tenant_config: Tenant configuration for document type normalization

    Returns:
        Dict structure: {email_key: {doc_type: [attachment_tuples]}}
        where email_key is f"{sender}|{received_time}"
    """
    # Import here to avoid circular imports
    from core.unified_file_analyzer import _normalize_document_type
    from core.interpreter.chatgpt_api import analyze_mail_and_pdf
    from core.file_processors.factory import file_processor_factory

    grouped = defaultdict(lambda: defaultdict(list))

    for attachment in attachments:
        if len(attachment) == 7:  # Multi-mailbox format
            file_bytes, filename, sender, received_time, mail_body, mime_type, mailbox_email = attachment
        else:  # Legacy single-mailbox format
            file_bytes, filename, sender, received_time, mail_body, mime_type = attachment
            mailbox_email = ""

        # Create unique email identifier
        email_key = f"{sender}|{received_time}"

        # Quick document type classification for grouping
        # We'll do a lightweight classification here just for grouping
        processor = file_processor_factory.get_processor(mime_type, filename)
        if processor:
            result = processor.process(file_bytes, filename)
            if result.success:
                # Quick ChatGPT classification for grouping
                analysis_result = analyze_mail_and_pdf(mail_body, result.text, "English")
                chatgpt_doc_type = analysis_result.get("doc_type", "Unknown")
                # Normalize document type to match config.json format
                doc_type = _normalize_document_type(chatgpt_doc_type, tenant_config)
            else:
                doc_type = "Unknown"
        else:
            doc_type = "Unknown"

        grouped[email_key][doc_type].append(attachment)

    return grouped


def process_tenants() -> None:
    tenants = list_tenants()
    if not tenants:
        print("⚠️  No tenants configured – nothing to do.")
        return

    for tenant in tenants:
        tenant_name, _creds_path, tenant_cfg, _token_cache = tenant
        print("\n==============================")
        print(f"🏢 Tenant: {tenant_name}  |  {datetime.now().isoformat(timespec='seconds')}")
        print("==============================")

        # Initialize tracking for this tenant
        try:
            tracking_config = setup_tracking_for_tenant(tenant_cfg)
            print(f"📊 Tracking initialized for {tenant_name} (enabled: {tracking_config.enabled})")
        except Exception as e:
            print(f"⚠️ Failed to initialize tracking for {tenant_name}: {e}")

        # Check if this tenant has multi-mailbox configuration
        mailbox_manager = MailboxConfigManager(tenant_cfg)

        if mailbox_manager.has_mailbox_configuration():
            # Use multi-mailbox reader
            print("📬 Multi-mailbox configuration detected")
            attachments = read_mail_multi_mailbox(tenant)

            if not attachments:
                continue

            headers = get_tenant_mail_headers(tenant)
            if not headers:
                print("❌ Could not obtain auth headers – skipping tenant.")
                continue

            # Group attachments by email and document type
            print("📊 Grouping attachments by email and document type...")
            grouped_attachments = group_attachments_by_email_and_type(attachments, tenant_cfg)

            # Process each email group
            for email_key, doc_type_groups in grouped_attachments.items():
                sender_info = email_key.split("|")[0]
                received_time = email_key.split("|", 1)[1]
                print(f"\n📧 Processing email from {sender_info} ({received_time})")

                # Process each document type group within this email
                for doc_type, attachment_list in doc_type_groups.items():
                    if len(attachment_list) == 1:
                        # Single document - process normally
                        file_bytes, filename, sender, received, mail_body, mime_type, mailbox_email = attachment_list[0]
                        print(f"\n🔄 Processing single {doc_type}: {filename} via {mailbox_email}")
                        analyze_file_bytes_with_mailbox(
                            file_bytes, tenant_cfg, filename, headers, mail_body, mime_type, mailbox_email
                        )
                    else:
                        # Multiple documents of same type - batch process
                        print(f"\n🔄 Batch processing {len(attachment_list)} {doc_type} documents")
                        process_document_batch(attachment_list, tenant_cfg, headers, doc_type)
        else:
            # Legacy single-mailbox behavior
            print("📧 Single-mailbox configuration detected")
            attachments = read_mail(tenant)
            if not attachments:
                continue

            headers = get_tenant_mail_headers(tenant)
            if not headers:
                print("❌ Could not obtain auth headers – skipping tenant.")
                continue

            # Group attachments by email and document type for legacy mode too
            print("📊 Grouping attachments by email and document type...")
            grouped_attachments = group_attachments_by_email_and_type(attachments, tenant_cfg)

            # Process each email group
            for email_key, doc_type_groups in grouped_attachments.items():
                sender_info = email_key.split("|")[0]
                received_time = email_key.split("|", 1)[1]
                print(f"\n� Processing email from {sender_info} ({received_time})")

                # Process each document type group within this email
                for doc_type, attachment_list in doc_type_groups.items():
                    if len(attachment_list) == 1:
                        # Single document - process normally
                        file_bytes, filename, sender, received, mail_body, mime_type = attachment_list[0]
                        print(f"\n🔄 Processing single {doc_type}: {filename}")
                        analyze_file_bytes(file_bytes, tenant_cfg, filename, headers, mail_body, mime_type)
                    else:
                        # Multiple documents of same type - batch process
                        print(f"\n🔄 Batch processing {len(attachment_list)} {doc_type} documents")
                        process_document_batch(attachment_list, tenant_cfg, headers, doc_type)


def process_document_batch(
    attachment_list: List[Tuple],
    tenant_cfg: Dict[str, Any],
    headers: Dict[str, str],
    doc_type: str
) -> None:
    """
    Process multiple documents of the same type from a single email as a batch.
    This function will analyze all documents together using batch-optimized ChatGPT prompts,
    then send a single consolidated notification email.
    """
    # Import here to avoid circular imports
    from core.unified_file_analyzer import analyze_file_bytes_internal
    from core.notification import send_batch_notification
    from core.interpreter.chatgpt_api import analyze_mail_and_batch_pdfs
    from core.file_processors.factory import file_processor_factory

    print(f"📦 Batch processing {len(attachment_list)} {doc_type} documents")

    batch_results = []
    mailbox_email = None
    mail_body = ""
    document_texts = []

    # First pass: Extract text from all documents and prepare for batch analysis
    for i, attachment in enumerate(attachment_list):
        if len(attachment) == 7:  # Multi-mailbox format
            file_bytes, filename, sender, received_time, mail_body, mime_type, mailbox_email = attachment
        else:  # Legacy single-mailbox format
            file_bytes, filename, sender, received_time, mail_body, mime_type = attachment
            mailbox_email = None

        print(f"  📄 Processing {i+1}/{len(attachment_list)}: {filename}")

        # Extract text from document for batch analysis
        processor = file_processor_factory.get_processor(mime_type, filename)
        if processor:
            result = processor.process(file_bytes, filename)
            if result.success:
                document_texts.append({
                    'filename': filename,
                    'text': result.text,
                    'file_bytes': file_bytes,
                    'mime_type': mime_type,
                    'sender': sender,
                    'received_time': received_time
                })
            else:
                print(f"❌ Failed to extract text from {filename}: {result.error}")
        else:
            print(f"❌ No processor found for {filename} ({mime_type})")

    # Second pass: Perform batch ChatGPT analysis
    batch_summary = "Batch processing completed."
    if document_texts:
        # Get preferred language from tenant config
        preferred_language = tenant_cfg.get("defaults", {}).get("preferred_language", "English")

        # Perform batch analysis with all documents
        try:
            batch_analysis = analyze_mail_and_batch_pdfs(mail_body, document_texts, preferred_language)
            batch_summary = batch_analysis.get("summary", "Batch processing completed.")
            print(f"📝 Batch analysis completed with summary: {batch_summary[:100]}...")
        except Exception as e:
            print(f"⚠️ Batch ChatGPT analysis failed, using individual summaries: {e}")
            batch_summary = None

    # Third pass: Process each document for upload and tracking
    for doc_info in document_texts:
        filename = doc_info['filename']
        file_bytes = doc_info['file_bytes']
        mime_type = doc_info['mime_type']

        # Use individual processing for upload and tracking, but skip ChatGPT analysis if we have batch summary
        if batch_summary and batch_summary != "Batch processing completed.":
            # Skip individual ChatGPT analysis since we have batch analysis
            doc_type_result, extracted_data, _ = analyze_file_bytes_internal(
                file_bytes, tenant_cfg, filename, headers, mail_body, mime_type, mailbox_email,
                send_notification=False, skip_chatgpt=True
            )
            summary = batch_summary
        else:
            # Fall back to individual analysis
            doc_type_result, extracted_data, summary = analyze_file_bytes_internal(
                file_bytes, tenant_cfg, filename, headers, mail_body, mime_type, mailbox_email,
                send_notification=False, skip_chatgpt=False
            )

        batch_results.append({
            'filename': filename,
            'file_bytes': file_bytes,
            'doc_type': doc_type_result,
            'extracted_data': extracted_data,
            'summary': summary,
            'sender': doc_info['sender'],
            'received_time': doc_info['received_time']
        })

    # Send consolidated notification if any documents were processed successfully
    if batch_results:
        try:
            send_batch_notification(
                doc_type, tenant_cfg, batch_results, headers, mailbox_email
            )
        except Exception as e:
            print(f"❌ Failed to send batch notification: {e}")


def analyze_file_bytes_with_mailbox(
    file_bytes: bytes,
    tenant_cfg: dict,
    filename: str,
    headers: dict,
    mail_body: str,
    mime_type: str,
    mailbox_email: str
) -> None:
    """
    Analyze file bytes with mailbox context for multi-mailbox setups.
    This wrapper ensures that notifications are sent from the correct mailbox.
    """
    # Import here to avoid circular imports
    from core.unified_file_analyzer import analyze_file_bytes_internal

    # Call the internal analyzer with mailbox context
    analyze_file_bytes_internal(
        file_bytes, tenant_cfg, filename, headers, mail_body, mime_type, mailbox_email
    )


def main() -> None:

    parser = argparse.ArgumentParser(description="Run the email-to-OneDrive pipeline")
    parser.add_argument("--once", action="store_true", help="Run a single cycle and exit")
    parser.add_argument(
        "--interval",
        type=int,
        default=CYCLE_SECONDS,
        help="Seconds to wait between cycles (default 300)",
    )
    args = parser.parse_args()

    if args.once:
        process_tenants()
    else:
        while True:
            process_tenants()
            print(f"\n⏳ Sleeping {args.interval}s …")
            time.sleep(args.interval)


if __name__ == "__main__":
    main()
