"""Email notification helper using Microsoft Graph API.

This module sends an email (optionally with the processed PDF attached) when
``notification.enabled`` is ``true`` for a document type in ``tenant_config``.

Public API
~~~~~~~~~~
    send_notification(doc_type, tenant_config, summary, pdf_bytes, filename, headers)
"""
from __future__ import annotations

import base64
import json
from typing import Dict, Any, List

import requests

__all__ = ["send_notification", "send_batch_notification"]

_GRAPH_ENDPOINT = "https://graph.microsoft.com/v1.0/me/sendMail"


def _merge_notification_settings(tenant_config: Dict[str, Any], doc_type: str) -> Dict[str, Any]:
    """Return effective notification settings for *doc_type*.

    Precedence: per-type override → tenant-wide defaults → hard-coded fallback.
    """
    defaults: Dict[str, Any] = tenant_config.get("defaults", {}).get("notification", {})
    overrides: Dict[str, Any] = (
        tenant_config.get("document_types", {}).get(doc_type, {}).get("notification", {})
    )

    merged = {**defaults, **overrides}
    
    # Check if notification is enabled via actions.notify
    default_actions = tenant_config.get("defaults", {}).get("actions", {})
    doc_actions = tenant_config.get("document_types", {}).get(doc_type, {}).get("actions", {})
    should_notify = doc_actions.get("notify", default_actions.get("notify", False))
    
    # Ensure critical keys exist
    merged.setdefault("enabled", should_notify)
    merged.setdefault("recipients", [])
    # Don't set a default email_template here - let send_notification handle it
    # This allows the system to use localized templates when no custom template is provided
    return merged


def _extract_first_name(full_name: str) -> str:
    """Extract first name from a full name string."""
    if not full_name:
        return "there"  # Friendly fallback

    # Split by space and take first part
    parts = full_name.strip().split()
    if parts:
        return parts[0]
    return "there"


def _build_message(
    subject: str,
    body_text: str,
    recipients: List[Dict[str, str]],
    pdf_bytes: bytes | None,
    filename: str | None,
) -> Dict[str, Any]:
    """Build the JSON payload for Graph ``/sendMail``."""
    msg: Dict[str, Any] = {
        "subject": subject,
        "body": {"contentType": "Text", "content": body_text},
        "toRecipients": [
            {
                "emailAddress": {"name": r.get("name", ""), "address": r["email"]}
            }
            for r in recipients
        ],
    }

    if pdf_bytes and filename:
        attachment = {
            "@odata.type": "#microsoft.graph.fileAttachment",
            "name": filename,
            "contentType": "application/pdf",
            "contentBytes": base64.b64encode(pdf_bytes).decode(),
        }
        msg.setdefault("attachments", []).append(attachment)

    return {"message": msg, "saveToSentItems": "false"}


def _get_localized_subject(doc_type: str, language: str) -> str:
    """Get localized email subject based on language."""

    # Subject translations
    subjects = {
        "English": f"New {doc_type} received",
        "Swedish": f"Nytt {doc_type} mottaget",
        "German": f"Neues {doc_type} erhalten",
        "French": f"Nouveau {doc_type} reçu",
        "Spanish": f"Nuevo {doc_type} recibido",
        "Italian": f"Nuovo {doc_type} ricevuto",
        "Dutch": f"Nieuw {doc_type} ontvangen",
        "Norwegian": f"Nytt {doc_type} mottatt",
        "Danish": f"Nyt {doc_type} modtaget",
        "Finnish": f"Uusi {doc_type} vastaanotettu"
    }

    return subjects.get(language, f"New {doc_type} received")


def _get_localized_template_fallback(language: str) -> str:
    """Get localized fallback template based on language."""

    templates = {
        "English": "Hi {recipient_name},\n\nA new {doc_type} has arrived and was auto-filed.\n\n{summary}\n\nPlease review the attachment.",
        "Swedish": "Hej {recipient_name},\n\nEtt nytt {doc_type} har anlänt och arkiverats automatiskt.\n\n{summary}\n\nVänligen granska bilagan.",
        "German": "Hallo {recipient_name},\n\nEin neues {doc_type} ist angekommen und wurde automatisch abgelegt.\n\n{summary}\n\nBitte überprüfen Sie den Anhang.",
        "French": "Bonjour {recipient_name},\n\nUn nouveau {doc_type} est arrivé et a été classé automatiquement.\n\n{summary}\n\nVeuillez examiner la pièce jointe.",
        "Spanish": "Hola {recipient_name},\n\nUn nuevo {doc_type} ha llegado y se ha archivado automáticamente.\n\n{summary}\n\nPor favor, revise el archivo adjunto.",
        "Italian": "Ciao {recipient_name},\n\nUn nuovo {doc_type} è arrivato ed è stato archiviato automaticamente.\n\n{summary}\n\nSi prega di rivedere l'allegato.",
        "Dutch": "Hallo {recipient_name},\n\nEen nieuw {doc_type} is aangekomen en automatisch gearchiveerd.\n\n{summary}\n\nControleer de bijlage.",
        "Norwegian": "Hei {recipient_name},\n\nEt nytt {doc_type} har ankommet og blitt automatisk arkivert.\n\n{summary}\n\nVennligst gjennomgå vedlegget.",
        "Danish": "Hej {recipient_name},\n\nEt nyt {doc_type} er ankommet og blevet automatisk arkiveret.\n\n{summary}\n\nGennemgå venligst vedhæftningen.",
        "Finnish": "Hei {recipient_name},\n\nUusi {doc_type} on saapunut ja arkistoitu automaattisesti.\n\n{summary}\n\nTarkista liite."
    }

    return templates.get(language, templates["English"])


def send_notification(
    doc_type: str,
    tenant_config: Dict[str, Any],
    summary: str,
    pdf_bytes: bytes,
    filename: str,
    headers: Dict[str, str],
    mailbox_email: str = None,
) -> None:
    """Send an email notification for *doc_type* if enabled in *tenant_config*.

    The function is *idempotent*: if notifications are disabled it just returns.

    Args:
        doc_type: Type of document
        tenant_config: Tenant configuration
        summary: Document summary
        pdf_bytes: PDF file bytes
        filename: Original filename
        headers: Authentication headers
        mailbox_email: Email address of the mailbox that received the document (for multi-mailbox setups)
    """
    # Import here to avoid circular imports
    from .mailbox_manager import MailboxConfigManager

    # Check if this is a multi-mailbox setup
    if mailbox_email and "mailboxes" in tenant_config:
        send_notification_multi_mailbox(
            doc_type, tenant_config, summary, pdf_bytes, filename, headers, mailbox_email
        )
        return

    # Legacy single-mailbox behavior
    settings = _merge_notification_settings(tenant_config, doc_type)
    if not settings.get("enabled", False):
        return  # Notification disabled → silent no-op

    recipients = settings.get("recipients", [])
    if not recipients:
        print("⚠️  Notification enabled but no recipients configured – skipping email")
        return

    # Get preferred language from tenant config
    preferred_language = tenant_config.get("defaults", {}).get("preferred_language", "English")

    # Get template (use configured template or localized fallback)
    template = settings.get("email_template")
    if not template:
        template = _get_localized_template_fallback(preferred_language)

    # Get localized subject
    subject = _get_localized_subject(doc_type, preferred_language)

    # Send individual emails to each recipient with personalized content
    for recipient in recipients:
        recipient_name = _extract_first_name(recipient.get("name", ""))

        # Format template with recipient-specific information
        personalized_body = template.format(
            doc_type=doc_type,
            summary=summary or "(no summary)",
            recipient_name=recipient_name
        )

        # Send to single recipient
        single_recipient = [recipient]
        payload = _build_message(subject, personalized_body, single_recipient, pdf_bytes, filename)

        resp = requests.post(_GRAPH_ENDPOINT, headers=headers, json=payload)
        if resp.status_code in (202, 200):
            print(f"📧 Notification email sent to {recipient.get('name', recipient['email'])}")
        else:
            try:
                err = resp.json()
            except ValueError:
                err = resp.text
            print(f"❌ Failed to send notification to {recipient.get('name', recipient['email'])}: {resp.status_code} {err}")


def send_notification_multi_mailbox(
    doc_type: str,
    tenant_config: Dict[str, Any],
    summary: str,
    pdf_bytes: bytes,
    filename: str,
    headers: Dict[str, str],
    mailbox_email: str,
) -> None:
    """
    Send email notification for multi-mailbox setup.
    Uses mailbox-specific configuration and sends from the specific mailbox.

    Args:
        doc_type: Type of document
        tenant_config: Tenant configuration
        summary: Document summary
        pdf_bytes: PDF file bytes
        filename: Original filename
        headers: Authentication headers
        mailbox_email: Email address of the mailbox that received the document
    """
    from .mailbox_manager import MailboxConfigManager

    mailbox_manager = MailboxConfigManager(tenant_config)

    # Check if notifications should be sent for this document type in this mailbox
    if not mailbox_manager.should_notify_for_document(mailbox_email, doc_type):
        print(f"📧 Notifications disabled for {doc_type} in mailbox {mailbox_email}")
        return

    # Get recipients for this specific mailbox and document type
    recipients = mailbox_manager.get_notification_recipients(mailbox_email, doc_type)
    if not recipients:
        print(f"⚠️ No recipients configured for {doc_type} in mailbox {mailbox_email}")
        return

    # Get preferred language from tenant config
    preferred_language = tenant_config.get("defaults", {}).get("preferred_language", "English")

    # Get merged configuration for this mailbox and document type
    merged_config = mailbox_manager.get_merged_document_config(mailbox_email, doc_type)

    # Get template (use configured template or localized fallback)
    template = merged_config.get("notification", {}).get("email_template")
    if not template:
        template = _get_localized_template_fallback(preferred_language)

    # Get localized subject
    subject = _get_localized_subject(doc_type, preferred_language)

    # Send individual emails to each recipient with personalized content
    for recipient in recipients:
        recipient_name = _extract_first_name(recipient.get("name", ""))

        # Format template with recipient-specific information
        personalized_body = template.format(
            doc_type=doc_type,
            summary=summary or "(no summary)",
            recipient_name=recipient_name
        )

        # Send to single recipient
        single_recipient = [recipient]
        payload = _build_message_from_mailbox(
            subject, personalized_body, single_recipient, pdf_bytes, filename, mailbox_email
        )

        resp = requests.post(_GRAPH_ENDPOINT, headers=headers, json=payload)
        if resp.status_code in (202, 200):
            print(f"📧 Notification email sent from {mailbox_email} to {recipient.get('name', recipient['email'])}")
        else:
            try:
                err = resp.json()
            except ValueError:
                err = resp.text
            print(f"❌ Failed to send notification from {mailbox_email} to {recipient.get('name', recipient['email'])}: {resp.status_code} {err}")


def _build_message_from_mailbox(
    subject: str,
    body: str,
    recipients: List[Dict[str, str]],
    pdf_bytes: bytes,
    filename: str,
    from_mailbox: str,
) -> Dict[str, Any]:
    """
    Build email message payload for sending from a specific mailbox.

    Args:
        subject: Email subject
        body: Email body
        recipients: List of recipients
        pdf_bytes: PDF attachment bytes
        filename: Attachment filename
        from_mailbox: Email address to send from

    Returns:
        Message payload for Microsoft Graph API
    """
    # Build the standard message
    message = _build_message(subject, body, recipients, pdf_bytes, filename)

    # Add the "from" field to specify which mailbox to send from
    message["message"]["from"] = {
        "emailAddress": {
            "address": from_mailbox
        }
    }

    return message


def _consolidate_summaries(batch_results: List[Dict[str, Any]], doc_type: str, language: str) -> str:
    """
    Consolidate multiple document summaries into a single, clean summary.
    Focus on concerns, out-of-spec parameters, and actionable items.
    """
    if not batch_results:
        return "(no summary)"

    # If only one document, return its summary
    if len(batch_results) == 1:
        return batch_results[0].get('summary', '(no summary)')

    # Collect all summaries and extract key information
    concerns = []
    document_info = []
    actions_needed = []

    for i, result in enumerate(batch_results, 1):
        filename = result.get('filename', f'Document {i}')
        summary = result.get('summary', '')

        if not summary or summary == '(no summary)':
            document_info.append(f"{i}. {filename}: Processed successfully")
            continue

        # Add document info
        document_info.append(f"{i}. {filename}")

        # Extract concerns and actions from summary
        # Look for common concern indicators
        summary_lower = summary.lower()
        if any(keyword in summary_lower for keyword in ['concern', 'issue', 'problem', 'warning', 'alert', 'out of spec', 'exceeds', 'below', 'above']):
            concerns.append(f"  - {filename}: {summary}")

        # Look for action indicators
        if any(keyword in summary_lower for keyword in ['review', 'check', 'verify', 'action', 'required', 'needed', 'urgent']):
            actions_needed.append(f"  - {filename}: {summary}")

    # Build consolidated summary
    consolidated_parts = []

    # Document info section
    consolidated_parts.append("---Document Info---")
    consolidated_parts.extend(document_info)

    # Concerns section (only if there are concerns)
    if concerns:
        consolidated_parts.append("\n---Concerns---")
        consolidated_parts.extend(concerns)

    # Actions section (only if actions are needed)
    if actions_needed:
        consolidated_parts.append("\n---Action Required---")
        consolidated_parts.extend(actions_needed)

    # If no concerns or actions, add a simple completion note
    if not concerns and not actions_needed:
        consolidated_parts.append("\n---Status---")
        consolidated_parts.append("All documents processed successfully with no issues detected.")

    return "\n".join(consolidated_parts)


def _build_batch_message(
    subject: str,
    body_text: str,
    recipients: List[Dict[str, str]],
    batch_results: List[Dict[str, Any]],
) -> Dict[str, Any]:
    """Build the JSON payload for Graph sendMail with multiple attachments."""
    msg: Dict[str, Any] = {
        "subject": subject,
        "body": {"contentType": "Text", "content": body_text},
        "toRecipients": [
            {
                "emailAddress": {"name": r.get("name", ""), "address": r["email"]}
            }
            for r in recipients
        ],
    }

    # Add all documents as attachments
    attachments = []
    for result in batch_results:
        file_bytes = result.get('file_bytes')
        filename = result.get('filename')

        if file_bytes and filename:
            attachment = {
                "@odata.type": "#microsoft.graph.fileAttachment",
                "name": filename,
                "contentType": "application/pdf",
                "contentBytes": base64.b64encode(file_bytes).decode(),
            }
            attachments.append(attachment)

    if attachments:
        msg["attachments"] = attachments

    return {"message": msg, "saveToSentItems": "false"}


def _get_batch_localized_template_fallback(language: str) -> str:
    """Get localized fallback template for batch notifications."""

    templates = {
        "English": "Hi {recipient_name},\n\n{count} new {doc_type} documents have arrived and were auto-filed.\n\n{summary}\n\nPlease review the attachments.",
        "Swedish": "Hej {recipient_name},\n\n{count} nya {doc_type} dokument har anlänt och arkiverats automatiskt.\n\n{summary}\n\nVänligen granska bilagorna.",
        "German": "Hallo {recipient_name},\n\n{count} neue {doc_type} Dokumente sind angekommen und wurden automatisch abgelegt.\n\n{summary}\n\nBitte überprüfen Sie die Anhänge.",
        "French": "Bonjour {recipient_name},\n\n{count} nouveaux documents {doc_type} sont arrivés et ont été classés automatiquement.\n\n{summary}\n\nVeuillez examiner les pièces jointes.",
        "Spanish": "Hola {recipient_name},\n\n{count} nuevos documentos {doc_type} han llegado y se han archivado automáticamente.\n\n{summary}\n\nPor favor, revise los archivos adjuntos.",
        "Italian": "Ciao {recipient_name},\n\n{count} nuovi documenti {doc_type} sono arrivati ed sono stati archiviati automaticamente.\n\n{summary}\n\nSi prega di rivedere gli allegati.",
        "Dutch": "Hallo {recipient_name},\n\n{count} nieuwe {doc_type} documenten zijn aangekomen en automatisch gearchiveerd.\n\n{summary}\n\nControleer de bijlagen.",
        "Norwegian": "Hei {recipient_name},\n\n{count} nye {doc_type} dokumenter har ankommet og blitt automatisk arkivert.\n\n{summary}\n\nVennligst gjennomgå vedleggene.",
        "Danish": "Hej {recipient_name},\n\n{count} nye {doc_type} dokumenter er ankommet og blevet automatisk arkiveret.\n\n{summary}\n\nGennemgå venligst vedhæftningerne.",
        "Finnish": "Hei {recipient_name},\n\n{count} uutta {doc_type} asiakirjaa on saapunut ja arkistoitu automaattisesti.\n\n{summary}\n\nTarkista liitteet."
    }

    return templates.get(language, templates["English"])


def _get_batch_localized_subject(doc_type: str, count: int, language: str) -> str:
    """Get localized email subject for batch notifications."""

    subjects = {
        "English": f"{count} new {doc_type} documents received",
        "Swedish": f"{count} nya {doc_type} dokument mottagna",
        "German": f"{count} neue {doc_type} Dokumente erhalten",
        "French": f"{count} nouveaux documents {doc_type} reçus",
        "Spanish": f"{count} nuevos documentos {doc_type} recibidos",
        "Italian": f"{count} nuovi documenti {doc_type} ricevuti",
        "Dutch": f"{count} nieuwe {doc_type} documenten ontvangen",
        "Norwegian": f"{count} nye {doc_type} dokumenter mottatt",
        "Danish": f"{count} nye {doc_type} dokumenter modtaget",
        "Finnish": f"{count} uutta {doc_type} asiakirjaa vastaanotettu"
    }

    return subjects.get(language, f"{count} new {doc_type} documents received")


def send_batch_notification(
    doc_type: str,
    tenant_config: Dict[str, Any],
    batch_results: List[Dict[str, Any]],
    headers: Dict[str, str],
    mailbox_email: str = None,
) -> None:
    """
    Send a consolidated email notification for multiple documents of the same type.

    Args:
        doc_type: Type of documents
        tenant_config: Tenant configuration
        batch_results: List of document processing results
        headers: Authentication headers
        mailbox_email: Email address of the mailbox that received the documents
    """
    if not batch_results:
        return

    # Import here to avoid circular imports
    from .mailbox_manager import MailboxConfigManager

    # Check if this is a multi-mailbox setup
    if mailbox_email and "mailboxes" in tenant_config:
        mailbox_manager = MailboxConfigManager(tenant_config)

        # Check if notifications should be sent for this document type in this mailbox
        if not mailbox_manager.should_notify_for_document(mailbox_email, doc_type):
            print(f"📧 Batch notifications disabled for {doc_type} in mailbox {mailbox_email}")
            return

        # Get recipients for this specific mailbox and document type
        recipients = mailbox_manager.get_notification_recipients(mailbox_email, doc_type)
        if not recipients:
            print(f"⚠️ No recipients configured for {doc_type} in mailbox {mailbox_email}")
            return
    else:
        # Legacy single-mailbox behavior
        settings = _merge_notification_settings(tenant_config, doc_type)
        if not settings.get("enabled", False):
            return  # Notification disabled → silent no-op

        recipients = settings.get("recipients", [])
        if not recipients:
            print("⚠️  Batch notification enabled but no recipients configured – skipping email")
            return

    # Get preferred language from tenant config
    preferred_language = tenant_config.get("defaults", {}).get("preferred_language", "English")

    # Consolidate summaries
    consolidated_summary = _consolidate_summaries(batch_results, doc_type, preferred_language)

    # Get batch template
    template = _get_batch_localized_template_fallback(preferred_language)

    # Get batch subject
    count = len(batch_results)
    subject = _get_batch_localized_subject(doc_type, count, preferred_language)

    # Send individual emails to each recipient with personalized content
    for recipient in recipients:
        recipient_name = _extract_first_name(recipient.get("name", ""))

        # Format template with recipient-specific information
        personalized_body = template.format(
            doc_type=doc_type,
            count=count,
            summary=consolidated_summary,
            recipient_name=recipient_name
        )

        # Send to single recipient with all attachments
        single_recipient = [recipient]

        if mailbox_email and "mailboxes" in tenant_config:
            # Multi-mailbox: send from specific mailbox
            payload = _build_batch_message_from_mailbox(
                subject, personalized_body, single_recipient, batch_results, mailbox_email
            )
        else:
            # Single-mailbox: send normally
            payload = _build_batch_message(subject, personalized_body, single_recipient, batch_results)

        resp = requests.post(_GRAPH_ENDPOINT, headers=headers, json=payload)
        if resp.status_code in (202, 200):
            if mailbox_email:
                print(f"📧 Batch notification sent from {mailbox_email} to {recipient.get('name', recipient['email'])} ({count} {doc_type} documents)")
            else:
                print(f"📧 Batch notification sent to {recipient.get('name', recipient['email'])} ({count} {doc_type} documents)")
        else:
            try:
                err = resp.json()
            except ValueError:
                err = resp.text
            print(f"❌ Failed to send batch notification to {recipient.get('name', recipient['email'])}: {resp.status_code} {err}")


def _build_batch_message_from_mailbox(
    subject: str,
    body: str,
    recipients: List[Dict[str, str]],
    batch_results: List[Dict[str, Any]],
    from_mailbox: str,
) -> Dict[str, Any]:
    """
    Build batch email message payload for sending from a specific mailbox.
    """
    # Build the standard batch message
    message = _build_batch_message(subject, body, recipients, batch_results)

    # Add the "from" field to specify which mailbox to send from
    message["message"]["from"] = {
        "emailAddress": {
            "address": from_mailbox
        }
    }

    return message


