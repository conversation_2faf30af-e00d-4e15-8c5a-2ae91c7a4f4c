"""Unified file analyzer for multi-format email attachments."""

from typing import Dict, <PERSON>, <PERSON><PERSON>, List
import json
import time

from core.file_processors.factory import file_processor_factory
from core.interpreter.chatgpt_api import analyze_mail_and_pdf
from core.router import resolve
from core.upload_onedrive import upload_pdf_to_onedrive
from core.notification import send_notification
from core.tracking import get_tracking_service, get_logger, OperationType
from core.subscription_manager import get_subscription_manager

__all__ = ["analyze_file_bytes", "analyze_file_bytes_internal"]


def _normalize_document_type(chatgpt_doc_type: str, tenant_config: Dict[str, Any]) -> str:
    """Map ChatGPT's document type output to the exact format used in config.json."""
    available_types = list(tenant_config.get("document_types", {}).keys())

    # First try exact match (case insensitive)
    chatgpt_lower = chatgpt_doc_type.lower()
    for doc_type in available_types:
        if doc_type.lower() == chatgpt_lower:
            return doc_type

    # Try with spaces converted to underscores
    chatgpt_underscore = chatgpt_lower.replace(" ", "_")
    for doc_type in available_types:
        if doc_type.lower() == chatgpt_underscore:
            return doc_type

    # Try partial matching
    chatgpt_words = set(chatgpt_lower.split())
    for doc_type in available_types:
        config_words = set(doc_type.lower().replace("_", " ").split())
        if chatgpt_words.issubset(config_words) or config_words.issubset(chatgpt_words):
            return doc_type

    return chatgpt_doc_type


def _get_notification_recipients(doc_type: str, tenant_config: Dict[str, Any]) -> List[str]:
    """Extract notification recipient email addresses for a document type."""
    recipients = []

    # Get document-specific recipients
    doc_config = tenant_config.get("document_types", {}).get(doc_type, {})
    doc_recipients = doc_config.get("notification", {}).get("recipients", [])

    # Get default recipients
    default_recipients = tenant_config.get("defaults", {}).get("notification", {}).get("recipients", [])

    # Combine recipients (document-specific takes precedence)
    all_recipients = doc_recipients if doc_recipients else default_recipients

    # Extract email addresses
    for recipient in all_recipients:
        if isinstance(recipient, dict) and "email" in recipient:
            recipients.append(recipient["email"])
        elif isinstance(recipient, str):
            recipients.append(recipient)

    return recipients


def analyze_file_bytes(
    file_bytes: bytes,
    tenant_config: Dict[str, Any],
    filename: str,
    headers: Dict[str, str],
    mail_body: str,
    mime_type: str = ""
) -> Tuple[str, Dict[str, Any], str]:
    """
    Legacy wrapper for backward compatibility.
    Calls the internal function without mailbox context.
    """
    return analyze_file_bytes_internal(
        file_bytes, tenant_config, filename, headers, mail_body, mime_type, None
    )


def analyze_file_bytes_internal(
    file_bytes: bytes,
    tenant_config: Dict[str, Any],
    filename: str,
    headers: Dict[str, str],
    mail_body: str = "",
    mime_type: str = "",
    mailbox_email: str = None,
    should_notify: bool = True,
    skip_chatgpt: bool = False,
    predetermined_doc_type: str = None,
) -> Tuple[str, Dict[str, Any], str]:
    """Process any supported file type and upload it according to tenant rules.

    Args:
        file_bytes: File content as bytes.
        tenant_config: Dict from <tenant>/config.json.
        filename: Original attachment filename.
        headers: Auth headers containing a valid Graph API access token.
        mail_body: Text body of the email containing the file.
        mime_type: MIME type of the file (optional).
        mailbox_email: Email address of the mailbox that received the document.

    Returns:
        Tuple of (doc_type, extracted_data, upload_folder).
    """
    # Initialize tracking and logging
    tenant_name = tenant_config.get("tenant_name", "unknown")
    mailbox_email = mailbox_email or "<EMAIL>"
    file_size = len(file_bytes)

    # Get tracking service and logger
    tracking_service = get_tracking_service()
    logger = get_logger(tenant_name)

    # Check subscription limits before processing
    subscription_manager = get_subscription_manager(tenant_name)
    if not subscription_manager.can_process_document():
        limits = subscription_manager.get_plan_limits()
        usage = subscription_manager.get_usage_stats()
        error_msg = f"Document processing limit reached ({usage.documents_this_month}/{limits.max_documents_per_month}). Please upgrade your subscription."
        print(f"🚫 {error_msg}")
        logger.log_error(filename, mailbox_email, error_msg, "subscription_limit")
        return "Limit Exceeded", {}, ""

    # Start tracking the processing operation
    with tracking_service.track_processing(
        tenant_name=tenant_name,
        mailbox_email=mailbox_email,
        filename=filename,
        file_size=file_size,
        operation_type=OperationType.FULL_PROCESSING
    ) as tracker:

        # Log processing start
        logger.log_processing_start(filename, mailbox_email, file_size, mime_type)

        # 1) Get appropriate file processor
        processor = file_processor_factory.get_processor(mime_type, filename)

        if not processor:
            error_msg = f"Unsupported file type: {filename} ({mime_type})"
            print(f"❌ {error_msg}")
            logger.log_error(filename, mailbox_email, error_msg, "file_processing")
            tracker.set_error(error_msg)
            return "Unknown", {}, ""

        # 2) Extract text from file
        result = processor.process(file_bytes, filename)

        if not result.success:
            error_msg = f"Failed processing {filename}: {result.error_message}"
            print(f"❌ {error_msg}")
            logger.log_error(filename, mailbox_email, error_msg, "text_extraction")
            tracker.set_error(error_msg)
            return "Unknown", result.metadata, ""

        text = result.text
        processing_metadata = result.metadata
    
        # 3) ChatGPT analysis - conditionally extract data
        if skip_chatgpt:
            # Skip ChatGPT analysis for batch processing
            if predetermined_doc_type:
                chatgpt_doc_type = predetermined_doc_type
                doc_type = predetermined_doc_type
            else:
                chatgpt_doc_type = "Unknown"
                doc_type = "Unknown"
            extracted_data = {"_processing_info": processing_metadata}
            summary = ""
            classification_time = 0
        else:
            # Get preferred language from tenant config
            preferred_language = tenant_config.get("defaults", {}).get("preferred_language", "English")

            classification_start = time.time()
            analysis_result = analyze_mail_and_pdf(mail_body, text, preferred_language)
            classification_time = int((time.time() - classification_start) * 1000)

            chatgpt_doc_type = analysis_result.get("doc_type", "Unknown")
            extracted_data = analysis_result.get("extracted_fields", {})
            summary = analysis_result.get("summary", "")

            # Add processing metadata to extracted data
            extracted_data["_processing_info"] = processing_metadata

            # 4) Normalize document type to match config.json format
            doc_type = _normalize_document_type(chatgpt_doc_type, tenant_config)

        # Log classification results
        logger.log_classification_result(
            filename, mailbox_email, doc_type,
            extracted_data=extracted_data,
            processing_time_ms=classification_time
        )

        # Set document type in tracker
        tracker.set_document_type(doc_type)
        tracker.set_extracted_data(extracted_data)

        if summary:
            print(f"📝 Summary: {summary}")
        print(f"📄 Document type (GPT): {chatgpt_doc_type}")
        if doc_type != chatgpt_doc_type:
            print(f"📄 Normalized to: {doc_type}")

        if extracted_data:
            print(f"🔑 Extracted fields: {json.dumps(extracted_data, ensure_ascii=False)}")

        # 5) Resolve routing
        upload_folder = resolve(doc_type, tenant_config, extracted_data)
        tracker.set_upload_folder(upload_folder)
    
        # 6) Action handling - from tenant_config
        actions = tenant_config.get("defaults", {}).get("actions", {})

        # Get document-specific actions if available
        doc_config = tenant_config.get("document_types", {}).get(doc_type, {})
        if not doc_config:
            print(f"ℹ️ Document type '{doc_type}' not found in config, using defaults")
            doc_actions = {}
        else:
            doc_actions = doc_config.get("actions", {})

        # Merge the actions, with document-specific taking precedence
        should_upload = doc_actions.get("upload", actions.get("upload", True))
        should_notify = doc_actions.get("notify", actions.get("notify", False))

        print(f"⚙️ Actions: upload={should_upload}, notify={should_notify}")

        # Track upload and notification results
        upload_success = True
        notification_success = True
        notification_recipients = []

        # 7) Upload handling
        if should_upload:
            if upload_folder:
                upload_start = time.time()
                # Note: We still use the PDF uploader for now, but it works for any binary data
                upload_success = upload_pdf_to_onedrive(file_bytes, filename, headers, folder=upload_folder)
                upload_time = int((time.time() - upload_start) * 1000)

                # Log upload result
                logger.log_upload_result(
                    filename, mailbox_email, doc_type, upload_folder, file_size,
                    upload_success, None if upload_success else "Upload operation failed",
                    upload_time
                )

                if not upload_success:
                    print("❌ Upload failed")
            else:
                upload_success = False
                print("ℹ️ No upload folder resolved; skipping upload")
                logger.log_upload_result(
                    filename, mailbox_email, doc_type, "No folder resolved", file_size,
                    False, "No upload folder could be resolved"
                )
        else:
            upload_success = True  # Not attempting upload, so consider it successful

        # 8) Notification handling
        if should_notify:
            # Get notification recipients
            notification_recipients = _get_notification_recipients(doc_type, tenant_config)

            notification_start = time.time()
            try:
                send_notification(doc_type, tenant_config, summary, file_bytes, filename, headers, mailbox_email)
                notification_success = True
                notification_time = int((time.time() - notification_start) * 1000)

                # Log notification success
                logger.log_notification_result(
                    filename, mailbox_email, doc_type, notification_recipients,
                    True, None, notification_time
                )
            except Exception as e:
                notification_success = False
                notification_time = int((time.time() - notification_start) * 1000)
                error_msg = str(e)

                # Log notification failure
                logger.log_notification_result(
                    filename, mailbox_email, doc_type, notification_recipients,
                    False, error_msg, notification_time
                )
                print(f"❌ Notification failed: {error_msg}")
        else:
            notification_success = True  # Not attempting notification, so consider it successful
            notification_recipients = _get_notification_recipients(doc_type, tenant_config) if should_notify else []

        # Set notification recipients in tracker
        if notification_recipients:
            tracker.set_notification_recipients(notification_recipients)

        # Log overall processing completion
        logger.log_processing_complete(
            filename, mailbox_email, doc_type, file_size,
            upload_success, notification_success, upload_folder,
            notification_recipients, extracted_data=extracted_data
        )

        # Set final tracker status
        if upload_success and notification_success:
            tracker.set_success()
            # Increment document count only on successful processing
            subscription_manager.increment_document_count()
            print(f"📊 Document count incremented for tenant {tenant_name}")
        elif upload_success or notification_success:
            tracker.set_partial_success()
            # Still count partial success as processed document
            subscription_manager.increment_document_count()
            print(f"📊 Document count incremented for tenant {tenant_name} (partial success)")
        else:
            tracker.set_error("Both upload and notification failed")

        return doc_type, extracted_data, summary
